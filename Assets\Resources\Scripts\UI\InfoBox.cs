
using System.Collections;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class InfoBox : MonoBehaviour
{
    public static InfoBox Instance { get; private set; }
    public Canvas canvas;
    public Camera uiCamera;
    Vector2 storedTouchPosition; // Store touch position when InfoBox is triggered
    Vector3 buttonWorldPosition; // Store button position when InfoBox is triggered
    public GameObject info;
    GameObject slot1, slot2;
    GameObject S1title, S2title;
    GameObject type;
    Image S1titleIcon, S2titleIcon, typeIcon;
    TextMeshProUGUI S1titleText, typeText, S2titleText;
    TextMeshProUGUI S1description, S2description;

    private bool isAnimating = false;

    //Animation
    Vector3 initialScale;
    // Start is called once before the first execution of Update after the MonoBehaviour is created

    void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
    }

    void Start()
    {
        Instance = this;

        // Info setup
        initialScale = info.transform.localScale;
        info.transform.localScale = Vector3.zero;
        info.SetActive(false);

        type = info.transform.GetChild(1).gameObject;
        slot1 = info.transform.GetChild(2).gameObject;
        slot2 = info.transform.GetChild(3).gameObject;

        typeIcon = type.transform.GetChild(0).GetComponent<Image>();
        typeText = type.transform.GetChild(1).GetComponent<TextMeshProUGUI>();

        S1title = slot1.transform.GetChild(0).gameObject;
        S2title = slot2.transform.GetChild(0).gameObject;

        S1titleIcon = S1title.transform.GetChild(0).GetComponent<Image>();
        S1titleText = S1title.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
        S1description = slot1.transform.GetChild(1).GetComponent<TextMeshProUGUI>();

        S2titleIcon = S2title.transform.GetChild(0).GetComponent<Image>();
        S2titleText = S2title.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
        S2description = slot2.transform.GetChild(1).GetComponent<TextMeshProUGUI>();


    }

    // Update is called once per frame
    void Update()
    {
        if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began)
        {
            if (info.activeSelf) HideInfo();
        }
    }

    public void DisplayAilmentOrSkill(bool isAilment, string ailment = null, string defense = null, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isAilment)
        {
            string spritePath = defense switch
            {
                "Fraco" => "Sprites/UI/Fraco",
                "Resiste" => "Sprites/UI/Resiste",
                "Imune" => "Sprites/UI/Nulo",
                "Repele" => "Sprites/UI/Repelir",
                "Absorver" => "Sprites/UI/Absorver",
                "Nulo" => "Sprites/UI/Nulo",
                "Resistência" => "Sprites/UI/Resiste",
                "Fraqueza" => "Sprites/UI/Fraco",
                "Repelir" => "Sprites/UI/Repelir",
                "Normal" => "Sprites/UI/Normal",
                _ => null
            };

            type.SetActive(false);
            slot1.SetActive(true);
            S1title.SetActive(true);
            S1titleIcon.gameObject.SetActive(true);
            S1titleIcon.sprite = Resources.Load<Sprite>($"Sprites/UI/{ailment}");
            S1titleText.gameObject.SetActive(true);
            S1titleText.text = ailment;
            S1description.text = GeneralInfo.GetAilmentDescriptionByName(ailment);

            slot2.SetActive(true);
            S2titleIcon.sprite = Resources.Load<Sprite>(spritePath);
            S2titleText.text = defense;
            string defenseId = GeneralInfo.GetElementalDefensesIdByName(defense);
            S2description.text = GeneralInfo.GetElementalDefensesDescription(defenseId);
        }
        else
        {
            string spritePath = defense switch
            {
                "Fraco" => "Sprites/UI/Fraco",
                "Resiste" => "Sprites/UI/Resiste",
                "Imune" => "Sprites/UI/Nulo",
                "Repele" => "Sprites/UI/Repelir",
                "Absorver" => "Sprites/UI/Absorver",
                "Nulo" => "Sprites/UI/Nulo",
                "Resistência" => "Sprites/UI/Resiste",
                "Fraqueza" => "Sprites/UI/Fraco",
                "Repelir" => "Sprites/UI/Repelir",
                "Normal" => "Sprites/UI/Normal",
                _ => null
            };

            type.SetActive(false);
            slot1.SetActive(false);

            slot2.SetActive(true);
            S2title.SetActive(true);
            S2titleIcon.gameObject.SetActive(true);
            S2titleIcon.sprite = Resources.Load<Sprite>(spritePath);
            S2titleText.gameObject.SetActive(true);
            S2titleText.text = defense;
            string defenseId = GeneralInfo.GetElementalDefensesIdByName(defense);
            S2description.text = GeneralInfo.GetElementalDefensesDescription(defenseId);
        }

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();

    }


    public void DisplayClassORArchetype(bool isClass, string name, string description, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isClass)
        {
            type.SetActive(true);
            slot1.SetActive(true);

            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Class");
            typeText.text = KeywordManager.GetWord("TEXT_CLASS");

            S1title.SetActive(true);
            S1titleIcon.gameObject.SetActive(false);
            S1titleText.gameObject.SetActive(true);
            S1titleText.text = name;
            S1description.text = description;

            slot2.SetActive(false);
        }
        else
        {
            type.SetActive(true);
            slot1.SetActive(true);

            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Archetype");
            typeText.text = KeywordManager.GetWord("TEXT_ARCHETYPE");

            S1title.SetActive(true);
            S1titleIcon.gameObject.SetActive(false);
            S1titleText.gameObject.SetActive(true);
            S1titleText.text = name;
            S1description.text = description;

            slot2.SetActive(false);
        }

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayModifier(string name, string description, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        type.SetActive(true);
        typeIcon.gameObject.SetActive(false);
        typeText.text = KeywordManager.GetWord("COLLECTIBLE_MODIFIERS");
        slot1.SetActive(true);

        S1title.SetActive(true);
        S1titleIcon.gameObject.SetActive(false);
        S1titleText.text = name;
        S1description.text = description;

        slot2.SetActive(false);

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayAgeOrHeight(bool isAge, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isAge)
        {
            type.SetActive(true);
            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Age");
            typeText.text = KeywordManager.GetWord("TITLE_AGE");

            slot1.gameObject.SetActive(true);
            S1title.gameObject.SetActive(false);
            S1description.text = KeywordManager.GetWord("INFO_AGE");

            slot2.gameObject.SetActive(false);
        }
        else
        {
            type.SetActive(true);
            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Height");
            typeText.text = KeywordManager.GetWord("TITLE_HEIGHT");

            slot1.gameObject.SetActive(true);
            S1title.gameObject.SetActive(false);
            S1description.text = KeywordManager.GetWord("INFO_HEIGHT");

            slot2.gameObject.SetActive(false);
        }

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayGender(string gender, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        type.SetActive(true);

        switch (gender)
        {
            case "1":
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/female-sign");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_FEMALE");
                break;
            case "2":
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/male-sign");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_MALE");
                break;
            default:
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Undefined");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_UNDEFINED");
                break;
        }

        slot1.gameObject.SetActive(true);
        S1title.gameObject.SetActive(true);
        S1titleIcon.gameObject.SetActive(false);
        S1titleText.text = KeywordManager.GetWord("TITLE_SEX");
        S1description.text = KeywordManager.GetWord("INFO_SEX");

        slot2.gameObject.SetActive(false);

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayGeneral(string message, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        type.SetActive(false);
        slot1.SetActive(true);
        S1title.SetActive(false);
        S1description.text = message;

        slot2.SetActive(false);

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }



    public void HideInfo()
    {
        if (isAnimating) return;
        isAnimating = true;

        // Hide animation
        info.transform.DOScale(Vector3.zero, 0.2f)
            .SetEase(Ease.InBack)
            .OnComplete(() =>
            {
                info.SetActive(false);
                isAnimating = false;
            });
    }

    private IEnumerator UpdateLayoutAndPositionNextFrame()
    {
        yield return null; // wait 1 frame

        LayoutRebuilder.ForceRebuildLayoutImmediate(info.GetComponent<RectTransform>());

        // Now determine position after layout is properly built
        DeterminePivotAndPosition();

        info.transform.DOScale(initialScale, 0.2f).SetEase(Ease.OutBack);
    }

    //Determine pivot and position based on the button position and screen space
    private void DeterminePivotAndPosition()
    {
        // Get the InfoBox RectTransform
        RectTransform infoRect = info.GetComponent<RectTransform>();

        // Use button position if available, otherwise fallback to cursor position
        Vector2 referencePos;
        if (buttonWorldPosition != Vector3.zero)
        {
            referencePos = buttonWorldPosition;
        }
        else if (storedTouchPosition != Vector2.zero)
        {
            Vector3 touchPos = storedTouchPosition;
            touchPos.z = uiCamera.nearClipPlane + 1f;
            referencePos = uiCamera.ScreenToWorldPoint(touchPos);
        }
        else
        {
            Debug.LogWarning("[InfoBox] No button or touch position available.");
            referencePos = Vector2.zero;
        }

        // Get InfoBox size by converting UI coordinates to world scale
        Vector2 infoSizeUI = infoRect.rect.size;

        // Calculate the scale factor by comparing screen coordinates
        float screenWidthWorld = 3.125f - (-3.125f); // From debug: Right - Left = 6.25
        float screenWidthUI = Screen.width; // 1200 from debug
        float worldToUIScale = screenWidthWorld / screenWidthUI;

        Vector2 infoSize = infoSizeUI * worldToUIScale;

        // Define padding in world space units
        float padding = 0.4f; // Adjust this value to increase/decrease padding

        // Define button offset in world space units
        float buttonOffset = 0.15f; // Distance from button to InfoBox

        // Get screen dimensions in world space (same approach as before but with proper Z)
        Vector3 screenBottomLeft = uiCamera.ScreenToWorldPoint(new Vector3(0, 0, uiCamera.nearClipPlane));
        Vector3 screenTopRight = uiCamera.ScreenToWorldPoint(new Vector3(Screen.width, Screen.height, uiCamera.nearClipPlane));

        // Apply padding to screen boundaries
        float screenTop = screenTopRight.y - padding;
        float screenBottom = screenBottomLeft.y + padding;
        float screenLeft = screenBottomLeft.x + padding;
        float screenRight = screenTopRight.x - padding;

        // Calculate proposed position with bottom-center pivot (InfoBox appears above button)
        Vector2 proposedPosition = (referencePos.x, referencePos.y + buttonOffset);

        // Calculate InfoBox bounds with bottom-center pivot
        float infoTop = proposedPosition.y + infoSize.y;
        float infoLeft = proposedPosition.x - (infoSize.x * 0.5f);
        float infoRight = proposedPosition.x + (infoSize.x * 0.5f);

        // Check if InfoBox would extend beyond screen boundaries (with padding)
        bool fitsAbove = infoTop <= screenTop;
        bool fitsHorizontally = infoLeft >= screenLeft && infoRight <= screenRight;

        Vector2 finalPivot;
        Vector2 finalPosition;

        if (fitsAbove)
        {
            // Use bottom-center pivot (InfoBox appears above button with positive offset)
            finalPivot = new Vector2(0.5f, 0f);
            finalPosition = new Vector2(referencePos.x, referencePos.y + buttonOffset);
        }
        else
        {
            // Use top-center pivot (InfoBox appears below button with negative offset)
            finalPivot = new Vector2(0.5f, 1f);
            finalPosition = new Vector2(referencePos.x, referencePos.y - buttonOffset);
        }

        // Ensure horizontal boundaries are respected regardless of pivot choice (with padding)
        if (!fitsHorizontally)
        {
            // Clamp the X position to keep InfoBox within screen bounds with padding
            float halfWidth = infoSize.x * 0.5f;
            finalPosition.x = Mathf.Clamp(finalPosition.x, screenLeft + halfWidth, screenRight - halfWidth);
        }

        // Apply the calculated pivot and position
        infoRect.pivot = finalPivot;
        info.transform.position = finalPosition;
    }

    private void StoreButtonPosition(Transform buttonTransform)
    {
        if (buttonTransform != null)
        {
            // Convert button's world position to screen position, then to world position for consistency
            Vector3 buttonScreenPos = uiCamera.WorldToScreenPoint(buttonTransform.position);
            buttonScreenPos.z = uiCamera.nearClipPlane + 1f;
            buttonWorldPosition = uiCamera.ScreenToWorldPoint(buttonScreenPos);
        }
        else
        {
            // Fallback to touch position if no button transform provided
            StoreTouchPosition();
            if (storedTouchPosition != Vector2.zero)
            {
                Vector3 touchPos = storedTouchPosition;
                touchPos.z = uiCamera.nearClipPlane + 1f;
                buttonWorldPosition = uiCamera.ScreenToWorldPoint(touchPos);
            }
            else
            {
                buttonWorldPosition = Vector3.zero;
            }
        }
    }

    private void StoreTouchPosition()
    {
        if (Input.touchCount > 0)
        {
            storedTouchPosition = Input.GetTouch(0).position;
        }
        else if (Input.GetMouseButtonDown(0)) // Fallback for editor
        {
            storedTouchPosition = Input.mousePosition;
        }
        else
        {
            storedTouchPosition = Vector2.zero;
        }

    }
}
