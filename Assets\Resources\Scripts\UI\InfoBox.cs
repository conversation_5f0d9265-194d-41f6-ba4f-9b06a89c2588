
using System.Collections;
using DG.Tweening;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class InfoBox : MonoBehaviour
{
    public static InfoBox Instance { get; private set; }
    public Canvas canvas;
    public Camera uiCamera;
    Vector2 storedTouchPosition; // Store touch position when InfoBox is triggered
    RectTransform buttonRectTransform; // Store button RectTransform for bounds calculation
    public GameObject info;
    GameObject slot1, slot2;
    GameObject S1title, S2title;
    GameObject type;
    Image S1titleIcon, S2titleIcon, typeIcon;
    TextMeshProUGUI S1titleText, typeText, S2titleText;
    TextMeshProUGUI S1description, S2description;

    public GameObject arrow1, arrow2;

    private bool isAnimating = false;

    //Animation
    Vector3 initialScale;
    // Start is called once before the first execution of Update after the MonoBehaviour is created

    void Awake()
    {
        // Singleton setup
        if (Instance != null && Instance != this)
        {
            Destroy(gameObject);
            return;
        }
        Instance = this;
    }

    void Start()
    {
        Instance = this;

        // Info setup
        initialScale = info.transform.localScale;
        info.transform.localScale = Vector3.zero;
        info.SetActive(false);
        arrow1.SetActive(false);
        arrow2.SetActive(false);

        type = info.transform.GetChild(1).gameObject;
        slot1 = info.transform.GetChild(2).gameObject;
        slot2 = info.transform.GetChild(3).gameObject;

        typeIcon = type.transform.GetChild(0).GetComponent<Image>();
        typeText = type.transform.GetChild(1).GetComponent<TextMeshProUGUI>();

        S1title = slot1.transform.GetChild(0).gameObject;
        S2title = slot2.transform.GetChild(0).gameObject;

        S1titleIcon = S1title.transform.GetChild(0).GetComponent<Image>();
        S1titleText = S1title.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
        S1description = slot1.transform.GetChild(1).GetComponent<TextMeshProUGUI>();

        S2titleIcon = S2title.transform.GetChild(0).GetComponent<Image>();
        S2titleText = S2title.transform.GetChild(1).GetComponent<TextMeshProUGUI>();
        S2description = slot2.transform.GetChild(1).GetComponent<TextMeshProUGUI>();


    }

    // Update is called once per frame
    void Update()
    {
        if (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began)
        {
            if (info.activeSelf) HideInfo();
        }
    }

    public void DisplayAilmentOrSkill(bool isAilment, string ailment = null, string defense = null, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isAilment)
        {
            string spritePath = defense switch
            {
                "Fraco" => "Sprites/UI/Fraco",
                "Resiste" => "Sprites/UI/Resiste",
                "Imune" => "Sprites/UI/Nulo",
                "Repele" => "Sprites/UI/Repelir",
                "Absorver" => "Sprites/UI/Absorver",
                "Nulo" => "Sprites/UI/Nulo",
                "Resistência" => "Sprites/UI/Resiste",
                "Fraqueza" => "Sprites/UI/Fraco",
                "Repelir" => "Sprites/UI/Repelir",
                "Normal" => "Sprites/UI/Normal",
                _ => null
            };

            type.SetActive(false);
            slot1.SetActive(true);
            S1title.SetActive(true);
            S1titleIcon.gameObject.SetActive(true);
            S1titleIcon.sprite = Resources.Load<Sprite>($"Sprites/UI/{ailment}");
            S1titleText.gameObject.SetActive(true);
            S1titleText.text = ailment;
            S1description.text = GeneralInfo.GetAilmentDescriptionByName(ailment);

            slot2.SetActive(true);
            S2titleIcon.sprite = Resources.Load<Sprite>(spritePath);
            S2titleText.text = defense;
            string defenseId = GeneralInfo.GetElementalDefensesIdByName(defense);
            S2description.text = GeneralInfo.GetElementalDefensesDescription(defenseId);
        }
        else
        {
            string spritePath = defense switch
            {
                "Fraco" => "Sprites/UI/Fraco",
                "Resiste" => "Sprites/UI/Resiste",
                "Imune" => "Sprites/UI/Nulo",
                "Repele" => "Sprites/UI/Repelir",
                "Absorver" => "Sprites/UI/Absorver",
                "Nulo" => "Sprites/UI/Nulo",
                "Resistência" => "Sprites/UI/Resiste",
                "Fraqueza" => "Sprites/UI/Fraco",
                "Repelir" => "Sprites/UI/Repelir",
                "Normal" => "Sprites/UI/Normal",
                _ => null
            };

            type.SetActive(false);
            slot1.SetActive(false);

            slot2.SetActive(true);
            S2title.SetActive(true);
            S2titleIcon.gameObject.SetActive(true);
            S2titleIcon.sprite = Resources.Load<Sprite>(spritePath);
            S2titleText.gameObject.SetActive(true);
            S2titleText.text = defense;
            string defenseId = GeneralInfo.GetElementalDefensesIdByName(defense);
            S2description.text = GeneralInfo.GetElementalDefensesDescription(defenseId);
        }

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();

    }


    public void DisplayClassORArchetype(bool isClass, string name, string description, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isClass)
        {
            type.SetActive(true);
            slot1.SetActive(true);

            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Class");
            typeText.text = KeywordManager.GetWord("TEXT_CLASS");

            S1title.SetActive(true);
            S1titleIcon.gameObject.SetActive(false);
            S1titleText.gameObject.SetActive(true);
            S1titleText.text = name;
            S1description.text = description;

            slot2.SetActive(false);
        }
        else
        {
            type.SetActive(true);
            slot1.SetActive(true);

            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Archetype");
            typeText.text = KeywordManager.GetWord("TEXT_ARCHETYPE");

            S1title.SetActive(true);
            S1titleIcon.gameObject.SetActive(false);
            S1titleText.gameObject.SetActive(true);
            S1titleText.text = name;
            S1description.text = description;

            slot2.SetActive(false);
        }

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayModifier(string name, string description, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        type.SetActive(true);
        typeIcon.gameObject.SetActive(false);
        typeText.text = KeywordManager.GetWord("COLLECTIBLE_MODIFIERS");
        slot1.SetActive(true);

        S1title.SetActive(true);
        S1titleIcon.gameObject.SetActive(false);
        S1titleText.text = name;
        S1description.text = description;

        slot2.SetActive(false);

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayAgeOrHeight(bool isAge, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        if (isAge)
        {
            type.SetActive(true);
            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Age");
            typeText.text = KeywordManager.GetWord("TITLE_AGE");

            slot1.gameObject.SetActive(true);
            S1title.gameObject.SetActive(false);
            S1description.text = KeywordManager.GetWord("INFO_AGE");

            slot2.gameObject.SetActive(false);
        }
        else
        {
            type.SetActive(true);
            typeIcon.gameObject.SetActive(true);
            typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Height");
            typeText.text = KeywordManager.GetWord("TITLE_HEIGHT");

            slot1.gameObject.SetActive(true);
            S1title.gameObject.SetActive(false);
            S1description.text = KeywordManager.GetWord("INFO_HEIGHT");

            slot2.gameObject.SetActive(false);
        }

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayGender(string gender, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        type.SetActive(true);

        switch (gender)
        {
            case "1":
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/female-sign");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_FEMALE");
                break;
            case "2":
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/male-sign");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_MALE");
                break;
            default:
                typeIcon.gameObject.SetActive(true);
                typeIcon.sprite = Resources.Load<Sprite>("Sprites/UI/Undefined");
                typeText.gameObject.SetActive(true);
                typeText.text = KeywordManager.GetWord("SEX_UNDEFINED");
                break;
        }

        slot1.gameObject.SetActive(true);
        S1title.gameObject.SetActive(true);
        S1titleIcon.gameObject.SetActive(false);
        S1titleText.text = KeywordManager.GetWord("TITLE_SEX");
        S1description.text = KeywordManager.GetWord("INFO_SEX");

        slot2.gameObject.SetActive(false);

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }

    public void DisplayGeneral(string message, Transform buttonTransform = null)
    {
        if (info.activeSelf)
        {
            HideInfo();
            return;
        }

        type.SetActive(false);
        slot1.SetActive(true);
        S1title.SetActive(false);
        S1description.text = message;

        slot2.SetActive(false);

        // Store button position or fallback to touch position
        StoreButtonPosition(buttonTransform);

        // Reset scale
        info.transform.localScale = Vector3.zero;

        // Force layout rebuild and determine position
        StartCoroutine(UpdateLayoutAndPositionNextFrame());

        info.SetActive(true);
        gameObject.transform.SetAsLastSibling();
    }



    public void HideInfo()
    {
        if (isAnimating) return;
        isAnimating = true;

        // Start synchronized hide animations for tooltip and arrows
        StartHideAnimations();
    }

    private IEnumerator UpdateLayoutAndPositionNextFrame()
    {
        yield return null; // wait 1 frame

        LayoutRebuilder.ForceRebuildLayoutImmediate(info.GetComponent<RectTransform>());

        // Now determine position after layout is properly built
        DeterminePivotAndPosition();

        // Reset arrow scales to zero before animation
        ResetArrowScales();

        // Start synchronized show animations for tooltip and arrows
        StartShowAnimations();
    }

    //Determine pivot and position based on the button edges and screen space
    private void DeterminePivotAndPosition()
    {
        // Get the InfoBox RectTransform
        RectTransform infoRect = info.GetComponent<RectTransform>();

        // Get InfoBox size by converting UI coordinates to world scale
        Vector2 infoSizeUI = infoRect.rect.size;

        // Calculate the scale factor by comparing screen coordinates
        float screenWidthWorld = 3.125f - (-3.125f); // From debug: Right - Left = 6.25
        float screenWidthUI = Screen.width; // 1200 from debug
        float worldToUIScale = screenWidthWorld / screenWidthUI;

        Vector2 infoSize = infoSizeUI * worldToUIScale;

        // Define padding in world space units
        float padding = 0.4f; // Adjust this value to increase/decrease padding

        // Define button offset in world space units
        float buttonOffset = 0.15f; // Distance from button edge to InfoBox

        // Get screen dimensions in world space
        Vector3 screenBottomLeft = uiCamera.ScreenToWorldPoint(new Vector3(0, 0, uiCamera.nearClipPlane));
        Vector3 screenTopRight = uiCamera.ScreenToWorldPoint(new Vector3(Screen.width, Screen.height, uiCamera.nearClipPlane));

        // Apply padding to screen boundaries
        float screenTop = screenTopRight.y - padding;
        float screenLeft = screenBottomLeft.x + padding;
        float screenRight = screenTopRight.x - padding;

        Vector2 buttonCenter;
        Vector2 originalButtonPivot; // Store original button pivot position for arrow alignment
        float buttonTopEdge, buttonBottomEdge;

        // Calculate button bounds if available, otherwise fallback to cursor position
        if (buttonRectTransform != null)
        {
            // Get button bounds in world space
            Vector3[] buttonCorners = new Vector3[4];
            buttonRectTransform.GetWorldCorners(buttonCorners);

            // Convert button corners to camera world space
            for (int i = 0; i < 4; i++)
            {
                Vector3 screenPos = uiCamera.WorldToScreenPoint(buttonCorners[i]);
                screenPos.z = uiCamera.nearClipPlane + 1f;
                buttonCorners[i] = uiCamera.ScreenToWorldPoint(screenPos);
            }

            // Use button's pivot position for horizontal alignment instead of geometric center
            Vector3 buttonPivotWorldPos = buttonRectTransform.position;
            Vector3 buttonPivotScreenPos = uiCamera.WorldToScreenPoint(buttonPivotWorldPos);
            buttonPivotScreenPos.z = uiCamera.nearClipPlane + 1f;
            Vector3 buttonPivotCameraSpace = uiCamera.ScreenToWorldPoint(buttonPivotScreenPos);

            buttonCenter = new Vector2(buttonPivotCameraSpace.x, buttonPivotCameraSpace.y);
            originalButtonPivot = buttonCenter; // Store original pivot position for arrow alignment
            buttonTopEdge = buttonCorners[1].y; // Top-left or top-right Y
            buttonBottomEdge = buttonCorners[0].y; // Bottom-left or bottom-right Y
        }
        else if (storedTouchPosition != Vector2.zero)
        {
            // Fallback to touch position
            Vector3 touchPos = storedTouchPosition;
            touchPos.z = uiCamera.nearClipPlane + 1f;
            Vector2 referencePos = uiCamera.ScreenToWorldPoint(touchPos);
            buttonCenter = referencePos;
            originalButtonPivot = referencePos; // Store original position for arrow alignment
            buttonTopEdge = referencePos.y;
            buttonBottomEdge = referencePos.y;
        }
        else
        {
            Debug.LogWarning("[InfoBox] No button or touch position available.");
            buttonCenter = Vector2.zero;
            originalButtonPivot = Vector2.zero; // Store original position for arrow alignment
            buttonTopEdge = 0f;
            buttonBottomEdge = 0f;
        }

        // Calculate proposed position for above placement (bottom edge of tooltip aligned to top edge of button)
        Vector2 proposedPositionAbove = new Vector2(buttonCenter.x, buttonTopEdge + buttonOffset);
        float infoTopWhenAbove = proposedPositionAbove.y + infoSize.y;

        // Check if InfoBox would fit above the button
        bool fitsAbove = infoTopWhenAbove <= screenTop;

        // Calculate horizontal bounds for both positions
        float infoLeft = buttonCenter.x - (infoSize.x * 0.5f);
        float infoRight = buttonCenter.x + (infoSize.x * 0.5f);
        bool fitsHorizontally = infoLeft >= screenLeft && infoRight <= screenRight;

        Vector2 finalPivot;
        Vector2 finalPosition;

        if (fitsAbove)
        {
            // Position above: bottom edge of tooltip aligned to top edge of button
            finalPivot = new Vector2(0.5f, 0f); // Bottom-center pivot
            finalPosition = new Vector2(buttonCenter.x, buttonTopEdge + buttonOffset);
        }
        else
        {
            // Position below: top edge of tooltip aligned to bottom edge of button
            finalPivot = new Vector2(0.5f, 1f); // Top-center pivot
            finalPosition = new Vector2(buttonCenter.x, buttonBottomEdge - buttonOffset);
        }

        // Ensure horizontal boundaries are respected regardless of pivot choice
        if (!fitsHorizontally)
        {
            // Clamp the X position to keep InfoBox within screen bounds with padding
            float halfWidth = infoSize.x * 0.5f;
            finalPosition.x = Mathf.Clamp(finalPosition.x, screenLeft + halfWidth, screenRight - halfWidth);
        }

        // Apply the calculated pivot and position
        infoRect.pivot = finalPivot;
        info.transform.position = finalPosition;

        // Manage arrow visibility and positioning using original button pivot (not affected by clamping)
        ManageArrowVisibilityAndPosition(originalButtonPivot.x, fitsAbove, infoRect, buttonRectTransform != null);
    }

    /// <summary>
    /// Manages arrow visibility and positioning based on tooltip placement direction
    /// </summary>
    /// <param name="buttonCenterX">The X coordinate of the button's center in world space</param>
    /// <param name="isAbovePlacement">True if tooltip is placed above the button, false if below</param>
    /// <param name="tooltipRect">The RectTransform of the tooltip for bounds calculation</param>
    /// <param name="hasButton">True if positioning relative to a button, false for touch-based fallback</param>
    private void ManageArrowVisibilityAndPosition(float buttonCenterX, bool isAbovePlacement, RectTransform tooltipRect, bool hasButton)
    {
        // Safety check: ensure arrows are assigned
        if (arrow1 == null || arrow2 == null)
        {
            Debug.LogWarning("[InfoBox] Arrow GameObjects not assigned. Skipping arrow positioning.");
            return;
        }

        // Only show arrows when we have a proper button reference
        // For touch-based positioning, hide arrows as there's no clear directional relationship
        if (!hasButton)
        {
            arrow1.SetActive(false);
            arrow2.SetActive(false);
            return;
        }

        if (isAbovePlacement)
        {
            // Tooltip is above button: show arrow1 (pointing down), hide arrow2
            arrow1.SetActive(true);
            arrow2.SetActive(false);

            // Position arrow1 at the bottom edge of tooltip, centered with button
            PositionArrow(arrow1, buttonCenterX, tooltipRect, ArrowPosition.Bottom);
        }
        else
        {
            // Tooltip is below button: show arrow2 (pointing up), hide arrow1
            arrow1.SetActive(false);
            arrow2.SetActive(true);

            // Position arrow2 at the top edge of tooltip, centered with button
            PositionArrow(arrow2, buttonCenterX, tooltipRect, ArrowPosition.Top);
        }
    }

    /// <summary>
    /// Positions an arrow relative to the tooltip bounds and button center
    /// </summary>
    /// <param name="arrow">The arrow GameObject to position</param>
    /// <param name="buttonCenterX">The X coordinate of the button's center in world space</param>
    /// <param name="tooltipRect">The RectTransform of the tooltip</param>
    /// <param name="position">Whether to position at top or bottom of tooltip</param>
    private void PositionArrow(GameObject arrow, float buttonCenterX, RectTransform tooltipRect, ArrowPosition position)
    {
        RectTransform arrowRect = arrow.GetComponent<RectTransform>();
        if (arrowRect == null) return;

        // Get tooltip bounds in world space
        Vector3[] tooltipCorners = new Vector3[4];
        tooltipRect.GetWorldCorners(tooltipCorners);

        // Convert tooltip corners to camera world space for consistency
        for (int i = 0; i < 4; i++)
        {
            Vector3 screenPos = uiCamera.WorldToScreenPoint(tooltipCorners[i]);
            screenPos.z = uiCamera.nearClipPlane + 1f;
            tooltipCorners[i] = uiCamera.ScreenToWorldPoint(screenPos);
        }

        // Calculate arrow position
        Vector3 arrowPosition;

        if (position == ArrowPosition.Bottom)
        {
            // Position at bottom edge of tooltip (for above placement)
            // Use bottom-left Y coordinate and center X with button
            arrowPosition = new Vector3(buttonCenterX, tooltipCorners[0].y, tooltipCorners[0].z);
        }
        else // ArrowPosition.Top
        {
            // Position at top edge of tooltip (for below placement)
            // Use top-left Y coordinate and center X with button
            arrowPosition = new Vector3(buttonCenterX, tooltipCorners[1].y, tooltipCorners[1].z);
        }

        // Apply the position to the arrow
        arrow.transform.position = arrowPosition;

        // Ensure arrow is properly layered (bring to front within tooltip)
        arrow.transform.SetAsLastSibling();
    }

    /// <summary>
    /// Resets arrow scales to zero before show animation
    /// </summary>
    private void ResetArrowScales()
    {
        if (arrow1 != null && arrow1.activeInHierarchy)
        {
            arrow1.transform.localScale = Vector3.zero;
        }

        if (arrow2 != null && arrow2.activeInHierarchy)
        {
            arrow2.transform.localScale = Vector3.zero;
        }
    }

    /// <summary>
    /// Starts synchronized show animations for tooltip and active arrows
    /// </summary>
    private void StartShowAnimations()
    {
        // Start tooltip animation
        info.transform.DOScale(initialScale, 0.2f).SetEase(Ease.OutBack);

        // Start arrow animations for active arrows
        if (arrow1 != null && arrow1.activeInHierarchy)
        {
            arrow1.transform.DOScale(Vector3.one, 0.2f).SetEase(Ease.OutBack);
        }

        if (arrow2 != null && arrow2.activeInHierarchy)
        {
            arrow2.transform.DOScale(Vector3.one, 0.2f).SetEase(Ease.OutBack);
        }
    }

    /// <summary>
    /// Starts synchronized hide animations for tooltip and active arrows
    /// </summary>
    private void StartHideAnimations()
    {
        // Start tooltip animation
        var tooltipTween = info.transform.DOScale(Vector3.zero, 0.2f)
            .SetEase(Ease.InBack)
            .OnComplete(() =>
            {
                info.SetActive(false);
                isAnimating = false;
                // Hide arrows after animation completes
                if (arrow1 != null) arrow1.SetActive(false);
                if (arrow2 != null) arrow2.SetActive(false);
            });

        // Start arrow animations for active arrows
        if (arrow1 != null && arrow1.activeInHierarchy)
        {
            arrow1.transform.DOScale(Vector3.zero, 0.2f).SetEase(Ease.InBack);
        }

        if (arrow2 != null && arrow2.activeInHierarchy)
        {
            arrow2.transform.DOScale(Vector3.zero, 0.2f).SetEase(Ease.InBack);
        }
    }

    /// <summary>
    /// Enum to specify arrow positioning relative to tooltip
    /// </summary>
    private enum ArrowPosition
    {
        Top,    // Position arrow at top edge of tooltip
        Bottom  // Position arrow at bottom edge of tooltip
    }

    private void StoreButtonPosition(Transform buttonTransform)
    {
        if (buttonTransform != null)
        {
            // Store the button's RectTransform for bounds calculation
            buttonRectTransform = buttonTransform.GetComponent<RectTransform>();
        }
        else
        {
            // Clear button RectTransform reference
            buttonRectTransform = null;

            // Fallback to touch position if no button transform provided
            StoreTouchPosition();
        }
    }

    private void StoreTouchPosition()
    {
        if (Input.touchCount > 0)
        {
            storedTouchPosition = Input.GetTouch(0).position;
        }
        else if (Input.GetMouseButtonDown(0)) // Fallback for editor
        {
            storedTouchPosition = Input.mousePosition;
        }
        else
        {
            storedTouchPosition = Vector2.zero;
        }

    }
}
